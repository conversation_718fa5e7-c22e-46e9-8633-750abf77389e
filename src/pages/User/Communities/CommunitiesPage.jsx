import React, { useState, useEffect, useRef, useContext } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { SkeletonLoader } from "Components/Skeleton";
import { showToast } from "Context/Global";
import { GlobalContext } from "Context/Global";
import { DMImage } from "Assets/images";
import ConfirmationModal from "Components/ConfirmationModal/ConfirmationModal";
import ReferralRecommendForm from "Components/ReferralRecommendForm/ReferralRecommendForm";
import debounce from "lodash/debounce";
import AddMembersForm from "./AddMembersForm";
import { UserAvatar } from "./UserAvatar";

// Add ReadMore component definition
const ReadMore = ({ text = '', maxWords = 100 }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle null/undefined/empty text
  if (!text) {
    return <p className="whitespace-pre-line text-[#b5b5b5]">No content available</p>;
  }

  const words = text.split(/\s+/);
  const needsReadMore = words.length > maxWords;

  const displayText = isExpanded ? text : words.slice(0, maxWords).join(' ') + (needsReadMore ? '...' : '');

  return (
    <div>
      <p className="whitespace-pre-line text-[#b5b5b5]">
        {displayText}
        {needsReadMore && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-[#7dd87d] hover:underline"
          >
            {isExpanded ? 'Read Less' : 'Read More'}
          </button>
        )}
      </p>
    </div>
  );
};

// Add industry icons mapping
const industryIcons = {
  Technology: (
    <svg
      className="h-8 w-8 text-[#7dd87d]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
      />
    </svg>
  ),
  Business: (
    <svg
      className="h-8 w-8 text-[#7dd87d]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
      />
    </svg>
  ),
  Finance: (
    <svg
      className="h-8 w-8 text-[#7dd87d]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  ),
  Healthcare: (
    <svg
      className="h-8 w-8 text-[#7dd87d]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
      />
    </svg>
  ),
  Education: (
    <svg
      className="h-8 w-8 text-[#7dd87d]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
      />
    </svg>
  ),
  // Add more industry icons as needed
};

const CrownIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="42"
    height="33"
    viewBox="0 0 42 33"
    fill="none"
  >
    <path
      d="M22.4766 5.79688C23.2781 5.30469 23.8125 4.41172 23.8125 3.40625C23.8125 1.85234 22.5539 0.59375 21 0.59375C19.4461 0.59375 18.1875 1.85234 18.1875 3.40625C18.1875 4.41875 18.7219 5.30469 19.5234 5.79688L15.4945 13.8547C14.8547 15.1344 13.1953 15.5 12.0773 14.607L5.8125 9.59375C6.16406 9.12266 6.375 8.53906 6.375 7.90625C6.375 6.35234 5.11641 5.09375 3.5625 5.09375C2.00859 5.09375 0.75 6.35234 0.75 7.90625C0.75 9.46016 2.00859 10.7188 3.5625 10.7188C3.57656 10.7188 3.59766 10.7188 3.61172 10.7188L6.825 28.3953C7.21172 30.5328 9.075 32.0938 11.2547 32.0938H30.7453C32.918 32.0938 34.7812 30.5398 35.175 28.3953L38.3883 10.7188C38.4023 10.7188 38.4234 10.7188 38.4375 10.7188C39.9914 10.7188 41.25 9.46016 41.25 7.90625C41.25 6.35234 39.9914 5.09375 38.4375 5.09375C36.8836 5.09375 35.625 6.35234 35.625 7.90625C35.625 8.53906 35.8359 9.12266 36.1875 9.59375L29.9227 14.607C28.8047 15.5 27.1453 15.1344 26.5055 13.8547L22.4766 5.79688Z"
      fill="#7DD87D"
    />
  </svg>
);

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const month = date.toLocaleString("default", { month: "short" });
  const day = date.getDate();
  const year = date.getFullYear();
  return `${month} ${day}, ${year}`;
};

const getInitials = (name) => {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

// UserAvatar component moved to separate file

const PostDetailsModal = ({ isOpen, onClose, post }) => {
  const [activeTab, setActiveTab] = useState("details");
  const [notes, setNotes] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState("");
  const [newTaskData, setNewTaskData] = useState({
    content: "",
    due_date: ""
  });
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  useEffect(() => {
    if (isOpen && post) {
      loadNotes();
      loadTasks();
    }
  }, [isOpen, post]);

  const loadNotes = async () => {
    try {
      const sdk = new MkdSDK();
      const notesResponse = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes?referral_id=${post.id.value}`,
        {},
        "GET"
      );

      if (!notesResponse.error) {
        // Sort notes by created_at in descending order (latest first)
        const sortedNotes = (notesResponse.list || []).sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at);
        });
        setNotes(sortedNotes);
      }
    } catch (err) {
      console.error("Failed to load notes:", err);
    }
  };

  const loadTasks = async () => {
    try {
      const sdk = new MkdSDK();
      const tasksResponse = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks?referral_id=${post.id.value}`,
        {},
        "GET"
      );

      if (!tasksResponse.error) {
        // Sort tasks by due_date in ascending order (closest due date first)
        const sortedTasks = (tasksResponse.list || []).sort((a, b) => {
          return new Date(a.due_date) - new Date(b.due_date);
        });
        setTasks(sortedTasks);
      }
    } catch (err) {
      console.error("Failed to load tasks:", err);
    }
  };

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/notes",
        {
          content: { value: newNoteContent },
          referral_id: { value: post?.id?.value }
        },
        "POST"
      );

      if (!response.error) {
        // Create a new note with current timestamp while waiting for API response
        const timestamp = new Date().toISOString();
        const newNote = {
          id: response.model?.id?.value || Date.now(),
          description: newNoteContent,
          created_at: response.model?.created_at?.value || timestamp,
        };

        // Add new note at the beginning of the array
        setNotes(prev => [newNote, ...prev]);
        setNewNoteContent("");
        setIsAddingNote(false);

        // Refresh notes to get the correct data from server
        loadNotes();

        showToast(globalDispatch, "Note added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add note");
      }
    } catch (err) {
      console.error("Error adding note:", err);
      showToast(globalDispatch, err.message || "Failed to add note", 5000, "error");
    }
  };

  const handleDeleteNote = async (noteId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${noteId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted note from the list
        setNotes(prev => prev.filter(note => note.id !== noteId));
        showToast(globalDispatch, "Note deleted successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to delete note");
      }
    } catch (err) {
      console.error("Error deleting note:", err);
      showToast(globalDispatch, err.message || "Failed to delete note", 5000, "error");
    }
  };

  const handleAddTask = async () => {
    if (!newTaskData.content.trim() || !newTaskData.due_date) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/tasks",
        {
          content: { value: newTaskData.content },
          due_date: { value: newTaskData.due_date },
          referral_id: { value: post?.id?.value }
        },
        "POST"
      );

      if (!response.error) {
        // Create a new task with current timestamp while waiting for API response
        const newTask = {
          id: response.model?.id || Date.now(),
          description: newTaskData.content,
          due_date: newTaskData.due_date,
          created_at: new Date().toISOString(),
          title: `Task added on ${new Date().toLocaleDateString()}`
        };

        // Add new task to the list
        setTasks(prev => [...prev, newTask]);
        setNewTaskData({ content: "", due_date: "" });
        setIsAddingTask(false);

        // Refresh tasks to get the correct data from server
        loadTasks();

        showToast(globalDispatch, "Task added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add task");
      }
    } catch (err) {
      console.error("Error adding task:", err);
      showToast(globalDispatch, err.message || "Failed to add task", 5000, "error");
    }
  };

  const handleDeleteTask = async (taskId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks/${taskId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted task from the list
        setTasks(prev => prev.filter(task => task.id !== taskId));
        showToast(globalDispatch, "Task deleted successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to delete task");
      }
    } catch (err) {
      console.error("Error deleting task:", err);
      showToast(globalDispatch, err.message || "Failed to delete task", 5000, "error");
    }
  };

  if (!isOpen || !post) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
          aria-hidden="true"
        />

        <div className="relative z-50 w-full max-w-3xl rounded-lg bg-[#161616] shadow-xl">
          <div className="flex flex-col h-[85vh]">
            <div className="sticky top-0 z-50 flex justify-between items-center bg-[#161616] p-6 border-b border-[#363636]">
              <div className="flex gap-3 items-center">
                <UserAvatar user={post.creator} />
                <div>
                  <h3 className="text-xl font-semibold text-[#eaeaea]">
                    {post.title.value}
                  </h3>
                  <p className="text-sm text-[#b5b5b5]">
                    {post.posted_by.value} -{" "}
                    {formatDate(post.created_at.value)}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-[#b5b5b5] hover:text-[#eaeaea] p-2"
              >
                <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            {/* Tabs */}
            <div className="border-b border-[#363636] px-6">
              <div className="flex gap-4">
                <button
                  onClick={() => setActiveTab("details")}
                  className={`border-b-2 py-2 px-1 text-sm ${
                    activeTab === "details"
                      ? "border-[#7dd87d] text-[#7dd87d]"
                      : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                  }`}
                >
                  Details
                </button>
                <button
                  onClick={() => setActiveTab("notes")}
                  className={`border-b-2 py-2 px-1 text-sm ${
                    activeTab === "notes"
                      ? "border-[#7dd87d] text-[#7dd87d]"
                      : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                  }`}
                >
                  Notes
                </button>
                <button
                  onClick={() => setActiveTab("tasks")}
                  className={`border-b-2 py-2 px-1 text-sm ${
                    activeTab === "tasks"
                      ? "border-[#7dd87d] text-[#7dd87d]"
                      : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                  }`}
                >
                  Tasks
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <div className={`h-full ${activeTab === "details" ? "block" : "hidden"}`}>
                <div className="space-y-6">
                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Type</h4>
                    <p className="text-[#b5b5b5]">{post?.type?.value || 'N/A'}</p>
                  </div>

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Description</h4>
                    <ReadMore text={post?.description?.value} maxWords={1000} />
                  </div>

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Deal Size</h4>
                    <p className="text-[#b5b5b5]">${post?.deal_size?.value || 'N/A'}</p>
                  </div>

                  {post?.expiration_date?.value && (
                    <div>
                      <h4 className="mb-2 font-medium text-[#eaeaea]">Expiration Date</h4>
                      <p className="text-[#b5b5b5]">{formatDate(post.expiration_date.value)}</p>
                    </div>
                  )}

                  {post?.description_image?.value && (
                    <div>
                      <h4 className="mb-2 font-medium text-[#eaeaea]">Attached Image</h4>
                      <img
                        src={post.description_image.value}
                        alt="Description"
                        className="max-h-96 rounded-lg object-contain"
                      />
                    </div>
                  )}

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Status</h4>
                    <span className="rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]">
                      {post?.status?.value || 'Unknown'}
                    </span>
                  </div>

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Referral Type</h4>
                    <p className="text-[#b5b5b5]">{post?.referral_type?.value || 'N/A'}</p>
                  </div>

                  {post?.recommendations?.value && post.recommendations.value.length > 0 && (
                    <div>
                      <h4 className="mb-2 font-medium text-[#eaeaea]">
                        Recommendations ({post.recommendations.value.length})
                      </h4>
                      <div className="space-y-2">
                        {post.recommendations.value.map((rec, index) => (
                          <div key={index} className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
                            <p className="text-[#eaeaea]">{rec.user?.name?.value}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className={`h-full ${activeTab === "notes" ? "block" : "hidden"}`}>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="text-lg font-medium text-[#eaeaea]">Notes</h4>
                    <button
                      onClick={() => setIsAddingNote(true)}
                      className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                    >
                      <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Add Note
                    </button>
                  </div>

                  {isAddingNote && (
                    <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
                      <textarea
                        value={newNoteContent}
                        onChange={(e) => setNewNoteContent(e.target.value)}
                        placeholder="Write your note..."
                        className="mb-4 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-[#eaeaea]"
                        rows={3}
                      />
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => setIsAddingNote(false)}
                          className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleAddNote}
                          className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          Add Note
                        </button>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    {notes.map((note) => (
                      <div
                        key={note.id}
                        className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-[#eaeaea]">{note.description}</p>
                            <p className="mt-2 text-sm text-[#b5b5b5]">
                              {formatDate(note.created_at)}
                            </p>
                          </div>
                          <button
                            onClick={() => handleDeleteNote(note.id)}
                            className="text-[#b5b5b5] hover:text-[#dc3545]"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className={`h-full ${activeTab === "tasks" ? "block" : "hidden"}`}>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="text-lg font-medium text-[#eaeaea]">Tasks</h4>
                    <button
                      onClick={() => setIsAddingTask(true)}
                      className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                    >
                      <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Add Task
                    </button>
                  </div>

                  {isAddingTask && (
                    <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
                      <div className="mb-4">
                        <label className="mb-2 block text-sm text-[#b5b5b5]">
                          Task Title
                        </label>
                        <input
                          type="text"
                          value={newTaskData.content}
                          onChange={(e) => setNewTaskData({...newTaskData, content: e.target.value})}
                          placeholder="Enter task title..."
                          className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                        />
                      </div>
                      <div className="mb-4">
                        <label className="mb-2 block text-sm text-[#b5b5b5]">
                          Due Date
                        </label>
                        <input
                          type="date"
                          value={newTaskData.due_date}
                          onChange={(e) => setNewTaskData({...newTaskData, due_date: e.target.value})}
                          className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => setIsAddingTask(false)}
                          className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleAddTask}
                          className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          Add Task
                        </button>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    {tasks.length === 0 ? (
                      <p className="text-center text-[#b5b5b5] py-4">No tasks yet. Create your first task!</p>
                    ) : (
                      tasks.map((task) => (
                        <div
                          key={task.id}
                          className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="text-[#eaeaea] font-medium">{task.description}</p>
                              <p className="mt-1 text-sm text-[#b5b5b5]">
                                Due: {formatDate(task.due_date)}
                              </p>
                            </div>
                            <button
                              onClick={() => handleDeleteTask(task.id)}
                              className="text-[#b5b5b5] hover:text-[#dc3545]"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const CommunitiesPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("joined");
  const [communities, setCommunities] = useState([]);
  const [allCommunities, setAllCommunities] = useState([]); // Store all communities for filtering
  const [recentActivity, setRecentActivity] = useState([]);
  const [activityLoading, setActivityLoading] = useState(true);
  const [showCommunityView, setShowCommunityView] = useState(false); // Initialize as false always
  const [loading, setLoading] = useState(true);
  const [communityPosts, setCommunityPosts] = useState([]);
  const [error, setError] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [showPaymentMethodModal, setShowPaymentMethodModal] = useState(false);
  const [searchError, setSearchError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingPost, setEditingPost] = useState(null);
  const [viewAll, setViewAll] = useState(true);
  const [selectedCommunity, setSelectedCommunity] = useState(null);
  const [showRepostModal, setShowRepostModal] = useState(false);
  const [referralToRepost, setReferralToRepost] = useState(null);
  const [joinedCommunities, setJoinedCommunities] = useState([]);
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const [paymentType, setPaymentType] = useState("card");
  const [formData, setFormData] = useState({
    card_number: "",
    exp_month: "",
    exp_year: "",
    cvc: "",
    name: "",
    is_default: false,
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [communityMembers, setCommunityMembers] = useState([]);
  const [loadingMembers, setLoadingMembers] = useState(false);
  const [showRecommendForm, setShowRecommendForm] = useState(false);
  const [selectedReferralId, setSelectedReferralId] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState(null);
  console.log(showCommunityView);

  // Initialize data based on location state
  useEffect(() => {
    const initializeWithLocationState = async () => {
      if (location.state?.showCommunity && location.state?.communityId) {
        try {
          const sdk = new MkdSDK();
          const response = await sdk.GetCommunityDetail(location.state.communityId);

          if (!response.error) {
            setSelectedCommunity(response.model);
            setShowCommunityView(true);
            // Load community posts
            loadCommunityPosts(location.state.communityId);
          }
        } catch (error) {
          console.error("Error loading community:", error);
          showToast(globalDispatch, "Failed to load community", 5000, "error");
        }
        // Clear the location state
        window.history.replaceState({}, document.title);
      }
    };

    initializeWithLocationState();
    loadCommunities();
    loadRecentActivity();
    loadJoinedCommunities();
  }, []);

  // Handle join_id parameter
  useEffect(() => {
    const handleJoinIdParam = async () => {
      const params = new URLSearchParams(location.search);
      const joinId = params.get("join_id");

      if (joinId) {
        try {
          const sdk = new MkdSDK();
          const response = await sdk.GetCommunityDetail(joinId);

          if (!response.error) {
            setSelectedCommunity(response.model);
            setShowJoinModal(true);
            const newUrl = window.location.pathname;
            window.history.replaceState({}, "", newUrl);
          } else {
            showToast(globalDispatch, "Community not found", 5000, "error");
          }
        } catch (error) {
          console.error("Error fetching community:", error);
          showToast(globalDispatch, "Failed to load community information", 5000, "error");
        }
      }
    };

    handleJoinIdParam();
  }, [location.search, globalDispatch]);

  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  const handleSearch = async (query) => {
    if (!query.trim()) {
      setIsSearching(false);
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    setSearchError("");
    setIsSearching(true);

    try {
      const sdk = new MkdSDK();
      const response = await sdk.SearchAll({ query, is_community: true });

      if (!response.error) {
        setSearchResults(response.list || []);
      } else {
        setSearchError(response.message);
      }
    } catch (err) {
      setSearchError(err.message);
    } finally {
      setSearchLoading(false);
    }
  };

  const loadCommunities = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunities();

      if (!response.error) {
        const communitiesList = response.list || [];
        console.log("Loaded communities:", communitiesList.length);

        // Store all communities for filtering
        setAllCommunities(communitiesList);
        setCommunities(communitiesList);
      } else {
        console.error("Error loading communities:", response.message);
        setError(response.message);
      }
    } catch (err) {
      console.error("Exception loading communities:", err);
      setError(err.message || "Failed to load communities");
    } finally {
      setLoading(false);
    }
  };

  // Function to filter communities based on search query
  const filterCommunities = (query) => {
    if (!query.trim() || !allCommunities.length) {
      // If no search query or no communities, show all communities
      setCommunities(allCommunities);
      return;
    }

    // Filter communities by title, description, and industry (case-insensitive)
    const searchTerm = query.toLowerCase();

    // Create a new array for filtered results to avoid modifying the original objects
    const filtered = allCommunities.filter(community => {
      // Check if title contains the search query
      const titleMatch = community.title?.value?.toLowerCase().includes(searchTerm);

      // Check if description contains the search query
      const descriptionMatch = community.description?.value?.toLowerCase().includes(searchTerm);

      // Check if industry contains the search query
      const industryMatch = community.industry_name?.value?.toLowerCase().includes(searchTerm);

      // Create a new object with search match flags instead of modifying the original
      const searchMatches = {
        title: titleMatch,
        description: descriptionMatch,
        industry: industryMatch
      };

      // Store search matches in a new property without modifying the original object
      community = {...community, _searchMatches: searchMatches};

      // Return true if any field matches
      return titleMatch || descriptionMatch || industryMatch;
    });

    console.log(`Filtered ${allCommunities.length} communities to ${filtered.length} results for query "${searchTerm}"`);

    // Only update state if we have results or if the query is empty
    if (filtered.length > 0 || !query.trim()) {
      setCommunities(filtered);
    }
  };
  // Create a debounced filter function
  const debouncedFilter = React.useCallback(
    debounce((query) => filterCommunities(query), 300),
    [allCommunities] // Add allCommunities as a dependency to recreate the function when it changes
  );

  // Effect to filter communities when search query changes
  useEffect(() => {
    if (allCommunities.length > 0 && searchQuery.trim()) {
      filterCommunities(searchQuery);
    }
  }, [searchQuery, allCommunities]);

  // Effect to handle tab changes
  useEffect(() => {
    console.log("Tab changed to:", activeTab);
    // If we're switching tabs, make sure we have the full list of communities
    if (allCommunities.length > 0) {
      setCommunities(allCommunities);
      // Clear search when changing tabs
      if (searchQuery) {
        setSearchQuery("");
      }
    }
  }, [activeTab]);

  // Handle search input changes
  const handleSearchInputChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    // If search is cleared, immediately show all communities
    if (!query.trim() && allCommunities.length > 0) {
      setCommunities(allCommunities);
    } else if (query.trim()) {
      // Only call the debounced filter if there's an actual query
      debouncedFilter(query);
    }
  };

  const loadRecentActivity = async () => {
    try {
      setActivityLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityFeed({ limit: 10 }); // Get last 10 activities

      if (!response.error) {
        setRecentActivity(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load activity:", err);
    } finally {
      setActivityLoading(false);
    }
  };
  const handleViewCommunity = async (communityId) => {
    console.log("Viewing community:", communityId);
    try {
      setLoading(true);
      const sdk = new MkdSDK();

      // Get community details
      const communityResponse = await sdk.GetCommunityDetail(communityId);
      if (!communityResponse.error) {
        setSelectedCommunity(communityResponse.model);
      }

      // Get community posts/referrals
      const postsResponse = await sdk.GetCommunityReferrals(communityId);
      if (!postsResponse.error) {
        setCommunityPosts(postsResponse.list || []);
      }

      // Show the community view
      setShowCommunityView(true);
      setActiveTab("joined"); // Ensure we're on joined tab
    } catch (err) {
      console.error("Failed to load community:", err);
      showToast(globalDispatch, "Failed to load community", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  // Add renderCommunityView function
  const renderCommunityView = () => {
    console.log("Rendering community view:", {
      selectedCommunity,
      communityPosts,
    });
    return (
      <div className="space-y-6">
        {/* Community Header */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex gap-4 items-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-lg">
              {selectedCommunity.title?.value?.charAt(0)}
            </div>
            <div>
              <h1 className="text-xl font-semibold text-[#eaeaea]">
                {selectedCommunity.title.value}
              </h1>
              <p className="text-sm text-[#b5b5b5]">
                {selectedCommunity.industry_name.value}
              </p>
            </div>
          </div>
          <div className="flex gap-4 items-center">
            {/* Hide Members and Chat buttons for RainmakerOS community (ID: 1) */}
            {!(selectedCommunity.title?.value === "RainmakerOS" && selectedCommunity.id?.value === 1) && (
              <>
                <button
                  onClick={() => {
                    loadCommunityMembers(selectedCommunity.id.value);
                    setShowMembersModal(true);
                  }}
                  className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                  Members ({selectedCommunity.member_count?.value || 0})
                </button>
                <button
                  onClick={() => navigate(`/member/communities/${selectedCommunity.id.value}/chat`)}
                  className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  Chat
                </button>
              </>
            )}
            <button
              onClick={() => {
                setShowCommunityView(false);
                setSelectedCommunity(null);
                setCommunityPosts([]);
              }}
              className="flex items-center gap-2 text-sm text-[#7dd87d]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Back
            </button>
          </div>
        </div>

        {/* Community Description */}
        <div className="rounded-lg border border-[#363636] bg-[#161616] p-6">
          <h3 className="mb-2 text-lg font-medium text-[#eaeaea]">About</h3>
          <p className="text-[#b5b5b5]">
            {selectedCommunity.description?.value}
          </p>
        </div>

        {/* Community Posts */}
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-[#eaeaea]">Posts</h2>

          </div>

          {communityPosts.length === 0 ? (
            <div className="flex flex-col items-center justify-center rounded-lg border border-[#363636] bg-[#161616] p-8">
              <svg
                className="mb-4 h-12 w-12 text-[#b5b5b5]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2"
                />
              </svg>
              <p className="text-lg font-medium text-[#eaeaea]">No posts yet</p>
              {/* <p className="mt-2 text-[#b5b5b5]">
                Be the first to create a post in this community
              </p> */}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {communityPosts.map((post) => (
                <div
                  key={post.id.value}
                  className="flex flex-col gap-2 rounded-lg border border-[#363636] bg-[#161616] p-4"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex gap-3">
                      <UserAvatar user={post.creator} />
                      <div>
                        <h3 className="text-lg font-semibold text-[#eaeaea]">
                          {post.title.value}
                        </h3>
                        <div className="flex gap-2">
                          <p className="text-sm text-[#b5b5b5]">
                            {post.creator.name.value} -{" "}
                            {formatDate(post.created_at.value)}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleViewDetails(post)}
                        className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                          />
                        </svg>
                        View Details
                      </button>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="mb-2 font-medium text-[#eaeaea]">
                      Description
                    </h4>
                    <p className="text-[#b5b5b5]">{post.description.value}</p>
                  </div>

                  <div className="flex gap-4 items-center">
                    <button
                      onClick={() => handleRefer(post.id.value)}
                      className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white"
                    >
                      Refer
                    </button>
                    <button
                      onClick={() => handleChat(post.id.value)}
                      className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
                    >
                      Chat
                    </button>
                    <button
                      onClick={() => handleRepost(post.id.value)}
                      className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
                    >
                      Repost
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <MembersModal
          isOpen={showMembersModal}
          onClose={() => setShowMembersModal(false)}
        />
      </div>
    );
  };

  const loadCommunityActivity = async (communityId) => {
    try {
      setActivityLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityFeed({
        community_id: communityId,
        limit: 10,
      });
      if (!response.error) {
        setRecentActivity(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load activity:", err);
    } finally {
      setActivityLoading(false);
    }
  };

  const loadCommunityPosts = async (communityId) => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityReferrals(communityId);
      if (!response.error) {
        // Set community posts, not communities
        setCommunityPosts(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load posts:", err);
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <div className="flex justify-between items-center mb-6">
      <div className="flex gap-4 items-center">
        {selectedCommunity ? (
          <>
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-lg">
              {selectedCommunity.title?.value?.charAt(0)}
            </div>
            <div>
              <h1 className="text-xl font-semibold text-[#eaeaea]">
                {selectedCommunity.title.value}
              </h1>
              <p className="text-sm text-[#b5b5b5]">
                {selectedCommunity.industry_name.value}
              </p>
            </div>
          </>
        ) : (
          <>
            <img
              src={DMImage}
              alt="RainmakerOS LLC"
              className="w-12 h-12 rounded-full"
            />
            <div>
              <h1 className="text-xl font-semibold text-[#eaeaea]">
                RainmakerOS LLC
              </h1>
              <p className="text-sm text-[#b5b5b5]">Community Management</p>
            </div>
          </>
        )}
      </div>
      <div className="flex gap-4 items-center">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchInputChange}
            placeholder="Search communities..."
            className="h-[42px] w-64 border border-[#363636] bg-[#242424] pl-4 pr-10 text-[#eaeaea] placeholder-[#666]"
          />
          {searchQuery ? (
            <button
              onClick={() => {
                setSearchQuery("");
                setCommunities(allCommunities);
              }}
              className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer"
              title="Clear search"
            >
              <svg
                className="w-4 h-4 text-[#b5b5b5] hover:text-[#eaeaea]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          ) : (
            <div
              style={{
                top: "50%",
                right: "10px",
                transform: "translateY(-50%)",
              }}
              className="absolute right-3 text-white"
            >
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none">
                <path
                  d="M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          )}
        </div>
        <button
          onClick={handleNewPost}
          className="flex h-[42px] items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
          Post Opportunity
        </button>
      </div>
    </div>
  );

  const renderTabs = () => (
    <div className="mb-6 border-b border-[#363636]">
      <button
        onClick={() => setActiveTab("joined")}
        className={`mr-6 border-b-2 pb-2 text-sm ${
          activeTab === "joined"
            ? "border-[#7dd87d] text-[#7dd87d]"
            : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
        }`}
      >
        My Communities
      </button>
      <button
        onClick={() => {
          setActiveTab("join");
          setShowCommunityView(false);
        }}
        className={`mr-6 border-b-2 pb-2 text-sm ${
          activeTab === "join"
            ? "border-[#7dd87d] text-[#7dd87d]"
            : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
        }`}
      >
        Join a Community
      </button>
      <button
        onClick={() => navigate("/member/communities/create")}
        className={`border-b-2 pb-2 text-sm ${
          activeTab === "create"
            ? "border-[#7dd87d] text-[#7dd87d]"
            : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
        }`}
      >
        Create a Community
      </button>
    </div>
  );

  const renderCommunityPosts = () => (
    <div className="space-y-4">
      {loading
        ? [...Array(2)].map((_, i) => (
            <SkeletonLoader key={i} className="w-full h-48 rounded-lg" />
          ))
        : communities.map((post) => (
            <div
              key={post.id.value}
              className="flex flex-col gap-2 rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
            >
              <div className="flex justify-between items-start">
                <div className="flex gap-3">
                  <UserAvatar user={post.creator} />
                  <div>
                    <h3 className="text-lg font-semibold text-[#eaeaea]">
                      {post.title.value}
                    </h3>
                    <div className="flex gap-2">
                      <p className="text-sm text-[#b5b5b5]">
                        {post.creator.name.value} -{" "}
                        {formatDate(post.created_at.value)}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleViewDetails(post)}
                    className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    View Details
                  </button>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="mb-2 font-medium text-[#eaeaea]">
                  Description
                </h4>
                <p className="text-[#b5b5b5]">{post.description.value}</p>
              </div>


              <div className="flex gap-4 items-center">
                <button
                  onClick={() => handleRefer(post.id.value)}
                  className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white"
                >
                  Refer
                </button>
                <button
                  onClick={() => handleChat(post.id.value)}
                  className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
                >
                  Chat
                </button>
                <button
                  onClick={() => handleRepost(post.id.value)}
                  className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
                >
                  Repost
                </button>
              </div>
            </div>
          ))}
    </div>
  );

  const renderCommunityList = () => (
    <div className="flex flex-1">
      <div style={{ width: "65%" }} className="">
      <div
          style={{
            padding: "16px",
            marginRight: "16px",
          }}
          className="mb-8 border-b border-[#363636] bg-black"
        >
          <h2 className="mb-4 text-sm font-semibold text-[#eaeaea]">
            Your Communities
          </h2>
          <div className="space-y-4">
            {loading
              ? [...Array(2)].map((_, i) => (
                  <SkeletonLoader key={i} className="w-full h-24 rounded-lg" />
                ))
              : communities
                  .filter((community) => community.is_member?.value === true)
                  .sort((a, b) => {
                    // Pin RainmakerOS community (title: "RainmakerOS", id: 1) to the top
                    const isARainmaker = a.title?.value === "RainmakerOS" && a.id?.value === 1;
                    const isBRainmaker = b.title?.value === "RainmakerOS" && b.id?.value === 1;

                    if (isARainmaker && !isBRainmaker) return -1; // a comes first
                    if (!isARainmaker && isBRainmaker) return 1;  // b comes first
                    return 0; // maintain original order for other communities
                  })
                  .map((community) => (
                    <div
                      key={community.id.value}
                      className="flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4"
                    >
                      <div className="flex gap-3 items-center">
                        {/* Pin icon for RainmakerOS community */}
                        {community.title?.value === "RainmakerOS" && community.id?.value === 1 && (
                          <svg
                            className="h-5 w-5 text-[#7dd87d]"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M16 12V4a1 1 0 00-.5-.87L12 1 8.5 3.13A1 1 0 008 4v8l-2 2v1h12v-1l-2-2z" />
                          </svg>
                        )}
                        {community.privacy?.value === "private" && (
                          <svg
                            className="h-5 w-5 text-[#b5b5b5]"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z" />
                          </svg>
                        )}
                        <div>
                          <h4 className="font-semibold text-[#eaeaea] flex items-center gap-2">
                            {community.title.value}
                            {/* Pinned badge for RainmakerOS community */}
                            {/* {community.title?.value === "RainmakerOS" && community.id?.value === 1 && (
                              <span className="text-xs bg-[#7dd87d] text-[#1e1e1e] px-2 py-1 rounded-full font-medium">
                                Pinned
                              </span>
                            )} */}
                          </h4>
                          <p className="text-sm text-[#b5b5b5]">
                            {community.privacy_settings?.value.who_can_find === "hidden"
                              ? "Hidden"
                              : community.privacy_settings?.value.who_can_find === "private"
                                ? "Private"
                                : "Public"}{" "}
                            Community • {community.member_count?.value || 0}{" "}
                            members
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() =>
                            handleViewCommunity(community.id.value)
                          }
                          className="rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          View
                        </button>
                        {community.is_admin?.value === true && (
                          <button
                            onClick={() =>
                              handleManageCommunity(community.id.value)
                            }
                            className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                          >
                            Manage
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
          </div>
        </div>

        <div
          style={{
            padding: "16px",
            marginRight: "16px",
          }}
          className="mb-8 border-b border-[#363636] bg-black"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-sm font-semibold text-[#eaeaea]">
              Available Communities
            </h2>
            <button
              onClick={() => setActiveTab("join")}
              className="text-sm text-[#7dd87d]"
            >
              View All
            </button>
          </div>
          <div className="space-y-4">
            {loading
              ? [...Array(2)].map((_, i) => (
                  <SkeletonLoader key={i} className="w-full h-24 rounded-lg" />
                ))
              : communities
                  .filter((community) => community.is_member?.value === false)
                  .map((community) => (
                    <div
                      key={community.id.value}
                      className="flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4"
                    >
                      <div className="flex gap-3 items-center">
                        {community.privacy?.value === "private" && (
                          <svg
                            className="h-5 w-5 text-[#b5b5b5]"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z" />
                          </svg>
                        )}
                        <div>
                          <h4 className="font-semibold text-[#eaeaea]">
                            {community.title.value}
                          </h4>
                          <p className="text-sm text-[#b5b5b5]">
                            {community.privacy?.value === "private"
                              ? "Private"
                              : "Public"}{" "}
                            Community • {community.member_count?.value || 0}{" "}
                            members
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleJoinCommunity(community)}
                        className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                      >
                        Join
                      </button>
                    </div>
                  ))}
          </div>
        </div>

        {/* <div
          style={{
            padding: "16px",
            marginRight: "16px",
          }}
          className="mb-8 border-b border-[#363636] bg-black"
        >
          <h2 className="mb-4 text-sm font-semibold text-[#eaeaea]">
            Your Communities
          </h2>
          <div className="space-y-4">
            {loading
              ? [...Array(2)].map((_, i) => (
                  <SkeletonLoader key={i} className="w-full h-24 rounded-lg" />
                ))
              : communities
                  .filter((community) => community.is_member?.value === true)
                  .map((community) => (
                    <div
                      key={community.id.value}
                      className="flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4"
                    >
                      <div className="flex gap-3 items-center">
                        {community.privacy?.value === "private" && (
                          <svg
                            className="h-5 w-5 text-[#b5b5b5]"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z" />
                          </svg>
                        )}
                        <div>
                          <h4 className="font-semibold text-[#eaeaea]">
                            {community.title.value}
                          </h4>
                          <p className="text-sm text-[#b5b5b5]">
                            {community.privacy?.value === "private"
                              ? "Private"
                              : "Public"}{" "}
                            Community • {community.member_count?.value || 0}{" "}
                            members
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() =>
                            handleViewCommunity(community.id.value)
                          }
                          className="rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          View
                        </button>
                        {community.is_admin?.value === true && (
                          <button
                            onClick={() =>
                              handleManageCommunity(community.id.value)
                            }
                            className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                          >
                            Manage
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
          </div>
        </div> */}
      </div>
      {renderRecentActivity()}
    </div>
  );

  const renderRecentActivity = () => (
    <div
      style={{
        width: "35%",
      }}
      className="space-y-6"
    >
      {/* Recent Activity */}
      <div className="p-4 bg-black rounded">
        <h2 className="mb-4 text-xl font-semibold text-[#eaeaea]">
          Recent Activity
        </h2>

        <div className="space-y-4">
          {recentActivity.map((activity) => (
            <div
              className="border-b border-[#363636] pb-4"
              key={activity.id.value}
            >
              <p className="text-sm text-[#eaeaea]">{activity.title.value}</p>
              <p className="text-xs text-[#b5b5b5]">
                {formatDate(new Date(activity.created_at.value))}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Add join handler
  const handleJoinCommunity = (community) => {
    setSelectedCommunity(community);
    setShowJoinModal(true);
  };

  // Add this new render function
  const renderGridView = () => (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-[#eaeaea]">
          All Communities
        </h2>
      </div>

      {/* Search Results Summary */}
      {searchQuery.trim() && communities.length > 0 && (
        <div className="mb-4 p-2 bg-[#1e1e1e] border border-[#363636] rounded-lg">
          <p className="text-[#eaeaea]">
            Found <span className="font-semibold text-[#7dd87d]">{communities.length}</span> communities matching "{searchQuery}"
          </p>
          <p className="text-[#b5b5b5] text-sm mt-1">
            Searching in title, description, and industry fields
          </p>
        </div>
      )}

      {/* No Results Message */}
      {searchQuery.trim() && communities.length === 0 && !loading && (
        <div className="flex flex-col items-center justify-center p-8 text-center mb-4">
          <svg className="w-16 h-16 mb-4 text-[#363636]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-xl font-medium text-[#eaeaea] mb-2">No communities found</h3>
          <p className="text-[#b5b5b5]">
            No communities matching "{searchQuery}" were found in titles, descriptions, or industry fields. Try a different search term.
          </p>
        </div>
      )}

      <div className="grid grid-cols-3 gap-6">
        {communities
          .filter((community) => community.is_member?.value === false)
          .map((community) => (
            <div
              key={community.id.value}
              className="flex flex-col rounded-lg border border-[#363636] bg-[#161616] p-6"
            >
              <div className="flex justify-between items-center mb-4">
                <div className="mr-4 mb-2">
                  {industryIcons[community.industry?.value] || (
                    <svg
                      className="h-8 w-8 text-[#7dd87d]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  )}
                </div>
                <div className="flex justify-between items-center w-full">
                  <h3 className="mb-1 font-semibold text-[#eaeaea]">
                    {community.title.value}
                  </h3>
                  <p className="text-sm text-[#b5b5b5]">
                    {community.member_count?.value || 0} members
                  </p>
                </div>
                {community.privacy?.value === "private" && (
                  <svg
                    className="h-5 w-5 text-[#b5b5b5]"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z" />
                  </svg>
                )}
              </div>
              <p className="mb-6 flex-grow text-sm text-[#b5b5b5]">
                {community.description?.value}
              </p>
              <div className="flex justify-between items-center">
                {/*  status of community (active) */}
                <p className="text-sm text-[#7dd87d]">Active</p>
                <button
                  onClick={() => handleJoinCommunity(community)}
                  className=" rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                >
                  Join Community
                </button>
              </div>
            </div>
          ))}
      </div>
    </div>
  );

  const PaymentMethodIcon = ({ type }) => {
    if (type === "paypal") {
      return (
        <svg
          className="h-5 w-5 text-[#7dd87d]"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M20.4 9.6H3.6C3.27 9.6 3 9.33 3 9V6C3 5.67 3.27 5.4 3.6 5.4H20.4C20.73 5.4 21 5.67 21 6V9C21 9.33 20.73 9.6 20.4 9.6Z" />
        </svg>
      );
    }
    return (
      <svg
        className="h-5 w-5 text-[#7dd87d]"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" />
      </svg>
    );
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handlePaymentTypeChange = (type) => {
    setPaymentType(type);
    setFormErrors({});
  };

  const handleRefer = async (referralId) => {
    setSelectedReferralId(referralId);
    setShowRecommendForm(true);
  };

  const handleChat = async (referralId) => {
    navigate(`/member/chat/${referralId}`);
  };

  const handleRepost = (referralId) => {
    setReferralToRepost(referralId);
    setShowRepostModal(true);
  };

  const RepostModal = ({ isOpen, onClose, referralId, communities }) => {
    if (!isOpen) return null;

    const { dispatch: globalDispatch } = useContext(GlobalContext);
    const [selectedCommunity, setSelectedCommunity] = useState("");
    const [loading, setLoading] = useState(false);

    const handleRepost = async () => {
      if (!selectedCommunity) return;
      setLoading(true);

      try {
        const sdk = new MkdSDK();
        await sdk.RepostReferral({
          referral_id: referralId,
          community_id: selectedCommunity,
        });

        showToast(
          globalDispatch,
          "Referral reposted successfully!",
          5000,
          "success"
        );
        onClose(true); // Pass true to trigger refresh
      } catch (err) {
        showToast(
          globalDispatch,
          err.message || "Failed to repost referral",
          5000,
          "error"
        );
      } finally {
        setLoading(false);
      }
    };

    return (
      <div className="flex fixed inset-0 z-50 justify-center items-center">
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={() => !loading && onClose()}
        />

        <div className="relative z-50 w-full max-w-md rounded-lg bg-[#161616] p-6 shadow-xl">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-[#eaeaea]">
              Select Community to Repost
            </h3>
            <button
              onClick={() => !loading && onClose()}
              className="text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50"
              disabled={loading}
            >
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          <select
            value={selectedCommunity}
            onChange={(e) => setSelectedCommunity(e.target.value)}
            className="mb-6 h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
            required
          >
            <option value="">Select a community</option>
            {communities.map((community) => (
              <option key={community.id.value} value={community.id.value}>
                {community.title.value}
              </option>
            ))}
          </select>

          <div className="flex gap-4 justify-end">
            <button
              onClick={() => !loading && onClose()}
              disabled={loading}
              className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleRepost}
              disabled={loading || !selectedCommunity}
              className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50"
            >
              {loading ? "Reposting..." : "Repost"}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const loadJoinedCommunities = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetJoinedCommunities();
      if (!response.error) {
        setJoinedCommunities(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load communities:", err);
    }
  };

  const JoinCommunityModal = ({ isOpen, onClose, community }) => {
    if (!isOpen) return null;

    const [step, setStep] = useState(1);
    const [loading, setLoading] = useState(false);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [formTouched, setFormTouched] = useState({});
    const [submitAttempted, setSubmitAttempted] = useState(false);
    const [joinFormData, setJoinFormData] = useState({
      firstName: "",
      lastName: "",
      industry: "",
      employeeCount: "",
      annualRevenue: "",
      goals: {
        buildPartnerships: false,
        gainAccess: false,
        getResources: false,
        learnStrategies: false,
        findSolutions: false,
        other: false,
      },
      otherGoal: "",
      businessChallenge: "",
      majorBreakthrough: "",
      whyJoin: "",
      meetingCommitment: "",
      additionalNotes: "",
      agreeToTerms: false,
      agreeToGuidelines: false,
    });

    // Add useEffect to fetch user details when component mounts
    useEffect(() => {
      const fetchUserDetails = async () => {
        try {
          const sdk = new MkdSDK();
          const response = await sdk.callRawAPI(
            "/v1/api/dealmaker/user/details",
            {},
            "GET"
          );

          if (!response.error && response.model) {
            setJoinFormData(prev => ({
              ...prev,
              firstName: response.model.first_name.value || "",
              lastName: response.model.last_name.value || ""
            }));
          }
        } catch (err) {
          console.error("Failed to fetch user details:", err);
        }
      };

      fetchUserDetails();
    }, []);

    const handleFieldChange = (field, value) => {
      setFormTouched(prev => ({ ...prev, [field]: true }));
      setJoinFormData(prev => ({ ...prev, [field]: value }));
    };

    const shouldShowError = (field) => {
      return (formTouched[field] || submitAttempted) && !joinFormData[field]?.trim();
    };

    const shouldShowGoalsError = () => {
      return (formTouched.goals || submitAttempted) &&
        !Object.values(joinFormData.goals).some(goal => goal === true);
    };

    const shouldShowOtherGoalError = () => {
      return (formTouched.otherGoal || submitAttempted) &&
        joinFormData.goals.other && !joinFormData.otherGoal.trim();
    };

    const handleNextClick = () => {
      setSubmitAttempted(true);
      if (validateFormStep1()) {
        setStep(2);
      }
    };

    const validateFormStep1 = () => {
      // Check required fields
      if (!joinFormData.firstName.trim()) return false;
      if (!joinFormData.lastName.trim()) return false;
      if (!joinFormData.industry.trim()) return false;
      if (!joinFormData.employeeCount.trim()) return false;
      if (!joinFormData.annualRevenue.trim()) return false;
      if (!joinFormData.businessChallenge.trim()) return false;
      if (!joinFormData.majorBreakthrough.trim()) return false;
      if (!joinFormData.whyJoin.trim()) return false;
      if (!joinFormData.meetingCommitment.trim()) return false;

      // Check if at least one goal is selected
      const hasSelectedGoal = Object.values(joinFormData.goals).some(goal => goal === true);
      if (!hasSelectedGoal) return false;

      // If other goal is selected, check if otherGoal text is provided
      if (joinFormData.goals.other && !joinFormData.otherGoal.trim()) return false;

      return true;
    };

    const handleSubmit = async (skipPayment = false) => {
      if (!joinFormData.agreeToTerms || !joinFormData.agreeToGuidelines) {
        showToast(
          globalDispatch,
          "Please agree to the terms and guidelines",
          5000,
          "error"
        );
        return;
      }

      if (!skipPayment) {
        setShowPaymentModal(true);
        return;
      }

      try {
        setLoading(true);
        const sdk = new MkdSDK();
        const response = await sdk.JoinCommunity({
          community_id: { value: community.id.value },
          first_name: { value: joinFormData.firstName },
          last_name: { value: joinFormData.lastName },
          industry: { value: joinFormData.industry },
          company_size: { value: joinFormData.employeeCount },
          meeting_commitment: { value: joinFormData.meetingCommitment },
          additional_notes: { value: joinFormData.additionalNotes },
          goals: {
            value: {
              networking: joinFormData.goals.buildPartnerships,
              businessDevelopment: joinFormData.goals.gainAccess,
              learningAndDevelopment: joinFormData.goals.getResources,
            },
          },
          skip_payment: skipPayment,
        });

        if (!response.error) {
          showToast(
            globalDispatch,
            "Successfully joined community!",
            5000,
            "success"
          );
          onClose(true);
          navigate("/member/communities");
          // window.location.reload(); // Add page refresh

          setShowJoinModal(false);
        } else {
          showToast(globalDispatch, response.message, 5000, "error");
        }
      } catch (err) {
        showToast(
          globalDispatch,
          err.message || "Failed to join community",
          5000,
          "error"
        );
      } finally {
        setLoading(false);
      }
    };

    const renderForm = () => (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-[#eaeaea]">
          Join {community?.title?.value} Community
        </h2>
        {renderStepIndicator()}

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              First Name *
            </label>
            <input
              type="text"
              value={joinFormData.firstName}
              onChange={(e) => handleFieldChange('firstName', e.target.value)}
              onBlur={() => setFormTouched(prev => ({ ...prev, firstName: true }))}
              className={`h-10 w-full rounded-lg border ${
                shouldShowError('firstName') ? 'border-red-500' : 'border-[#363636]'
              } bg-[#1e1e1e] px-4 text-[#eaeaea]`}
              placeholder="Enter first name"
              required
            />
            {shouldShowError('firstName') && (
              <p className="mt-1 text-xs text-red-500">First name is required</p>
            )}
          </div>
          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Last Name *
            </label>
            <input
              type="text"
              value={joinFormData.lastName}
              onChange={(e) => handleFieldChange('lastName', e.target.value)}
              onBlur={() => setFormTouched(prev => ({ ...prev, lastName: true }))}
              className={`h-10 w-full rounded-lg border ${
                shouldShowError('lastName') ? 'border-red-500' : 'border-[#363636]'
              } bg-[#1e1e1e] px-4 text-[#eaeaea]`}
              placeholder="Enter last name"
              required
            />
            {shouldShowError('lastName') && (
              <p className="mt-1 text-xs text-red-500">Last name is required</p>
            )}
          </div>
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            What industry is your business in? *
          </label>
          <select
            value={joinFormData.industry}
            onChange={(e) => handleFieldChange('industry', e.target.value)}
            onBlur={() => setFormTouched(prev => ({ ...prev, industry: true }))}
            className={`h-10 w-full appearance-none rounded-lg border ${
              shouldShowError('industry') ? 'border-red-500' : 'border-[#363636]'
            } bg-[#1e1e1e] px-4 text-[#eaeaea]`}
            required
          >
            <option value="">Select your industry</option>
            <option value="technology">Technology</option>
            <option value="finance">Finance</option>
            <option value="healthcare">Healthcare</option>
            <option value="education">Education</option>
            <option value="retail">Retail</option>
          </select>
          {shouldShowError('industry') && (
            <p className="mt-1 text-xs text-red-500">Industry is required</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            How many employees does your business have? *
          </label>
          <select
            value={joinFormData.employeeCount}
            onChange={(e) => handleFieldChange('employeeCount', e.target.value)}
            onBlur={() => setFormTouched(prev => ({ ...prev, employeeCount: true }))}
            className={`h-10 w-full appearance-none rounded-lg border ${
              shouldShowError('employeeCount') ? 'border-red-500' : 'border-[#363636]'
            } bg-[#1e1e1e] px-4 text-[#eaeaea]`}
            required
          >
            <option value="">Select range</option>
            <option value="1-10">1-10</option>
            <option value="11-50">11-50</option>
            <option value="51-200">51-200</option>
            <option value="201-500">201-500</option>
            <option value="500+">500+</option>
          </select>
          {shouldShowError('employeeCount') && (
            <p className="mt-1 text-xs text-red-500">Employee count is required</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            What's your approximate annual revenue? *
          </label>
          <select
            value={joinFormData.annualRevenue}
            onChange={(e) => handleFieldChange('annualRevenue', e.target.value)}
            onBlur={() => setFormTouched(prev => ({ ...prev, annualRevenue: true }))}
            className={`h-10 w-full appearance-none rounded-lg border ${
              shouldShowError('annualRevenue') ? 'border-red-500' : 'border-[#363636]'
            } bg-[#1e1e1e] px-4 text-[#eaeaea]`}
            required
          >
            <option value="">Select range</option>
            <option value="under_100k">under 100K</option>
            <option value="100k_500k">100K - 500K</option>
            <option value="500k_1m">500K - 1M</option>
            <option value="1m_5m">1M - 5M</option>
            <option value="5m_plus">5M+</option>
          </select>
          {shouldShowError('annualRevenue') && (
            <p className="mt-1 text-xs text-red-500">Annual revenue is required</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            What are your top 3 goals for joining this community? *
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={joinFormData.goals.buildPartnerships}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    goals: {
                      ...prev.goals,
                      buildPartnerships: e.target.checked,
                    },
                  }))
                }
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">
                Build partnerships to grow my business
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={joinFormData.goals.gainAccess}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    goals: { ...prev.goals, gainAccess: e.target.checked },
                  }))
                }
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">
                Gain access to investors and capital partners
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={joinFormData.goals.getResources}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    goals: { ...prev.goals, getResources: e.target.checked },
                  }))
                }
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">
                Get access to new resources and opportunities
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={joinFormData.goals.learnStrategies}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    goals: { ...prev.goals, learnStrategies: e.target.checked },
                  }))
                }
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">
                Learn strategies to leverage relationships
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={joinFormData.goals.findSolutions}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    goals: { ...prev.goals, findSolutions: e.target.checked },
                  }))
                }
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">
                Find solutions to specific business challenges
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={joinFormData.goals.other}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    goals: { ...prev.goals, other: e.target.checked },
                  }))
                }
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">Other:</span>
            </label>
            {joinFormData.goals.other && (
              <input
                type="text"
                value={joinFormData.otherGoal}
                onChange={(e) =>
                  setJoinFormData((prev) => ({
                    ...prev,
                    otherGoal: e.target.value,
                  }))
                }
                className="mt-2 h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
                placeholder="Please specify"
              />
            )}
          </div>
          {shouldShowGoalsError() && (
            <p className="mt-1 text-xs text-red-500">Please select at least one goal</p>
          )}
          {shouldShowOtherGoalError() && (
            <p className="mt-1 text-xs text-red-500">Please specify your other goal</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            What is your biggest business challenge that needs solving sooner
            rather than later? *
          </label>
          <textarea
            value={joinFormData.businessChallenge}
            onChange={(e) => handleFieldChange('businessChallenge', e.target.value)}
            onBlur={() => setFormTouched(prev => ({ ...prev, businessChallenge: true }))}
            className={`h-32 w-full rounded-lg border ${
              shouldShowError('businessChallenge') ? 'border-red-500' : 'border-[#363636]'
            } bg-[#1e1e1e] p-4 text-[#eaeaea]`}
            placeholder="Describe your challenge..."
            required
          />
          {shouldShowError('businessChallenge') && (
            <p className="mt-1 text-xs text-red-500">Business challenge is required</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            If you could achieve one major breakthrough in your business this
            year, what would it be? *
          </label>
          <textarea
            value={joinFormData.majorBreakthrough}
            onChange={(e) => handleFieldChange('majorBreakthrough', e.target.value)}
            onBlur={() => setFormTouched(prev => ({ ...prev, majorBreakthrough: true }))}
            className={`h-32 w-full rounded-lg border ${
              shouldShowError('majorBreakthrough') ? 'border-red-500' : 'border-[#363636]'
            } bg-[#1e1e1e] p-4 text-[#eaeaea]`}
            placeholder="Describe your desired breakthrough..."
            required
          />
          {shouldShowError('majorBreakthrough') && (
            <p className="mt-1 text-xs text-red-500">Major breakthrough is required</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            Why do you want to be part of {community?.title?.value}? *
          </label>
          <textarea
            value={joinFormData.whyJoin}
            onChange={(e) => handleFieldChange('whyJoin', e.target.value)}
            onBlur={() => setFormTouched(prev => ({ ...prev, whyJoin: true }))}
            className={`h-32 w-full rounded-lg border ${
              shouldShowError('whyJoin') ? 'border-red-500' : 'border-[#363636]'
            } bg-[#1e1e1e] p-4 text-[#eaeaea]`}
            placeholder="Share your reasons..."
            required
          />
          {shouldShowError('whyJoin') && (
            <p className="mt-1 text-xs text-red-500">This field is required</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            How much time can you realistically commit to meeting with members
            and engaging in potential deals each month? *
          </label>
          <div className="space-y-2">
            {["1-2 hours", "3-5 hours", "6+ hours"].map((option) => (
              <label key={option} className="flex items-center">
                <input
                  type="radio"
                  name="meetingCommitment"
                  value={option.toLowerCase()}
                  checked={joinFormData.meetingCommitment === option.toLowerCase()}
                  onChange={(e) => handleFieldChange('meetingCommitment', e.target.value)}
                  onBlur={() => setFormTouched(prev => ({ ...prev, meetingCommitment: true }))}
                  className="mr-2"
                />
                <span className="text-sm text-[#eaeaea]">{option}</span>
              </label>
            ))}
          </div>
          {shouldShowError('meetingCommitment') && (
            <p className="mt-1 text-xs text-red-500">Please select your time commitment</p>
          )}
        </div>

        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">
            Is there anything else you'd like us to know?
          </label>
          <textarea
            value={joinFormData.additionalNotes}
            onChange={(e) => handleFieldChange('additionalNotes', e.target.value)}
            className="h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]"
            placeholder="Share any additional information..."
          />
        </div>

        <div className="flex justify-end">
          <button
            onClick={handleNextClick}
            disabled={submitAttempted && !validateFormStep1()}
            className="rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    );

    const renderStepIndicator = () => (
      <div className="flex justify-center items-center mb-6">
        {[1, 2, 3].map((stepNumber) => (
          <React.Fragment key={stepNumber}>
            <div
              onClick={() => stepNumber < step && setStep(stepNumber)}
              className={`h-2 w-2 rounded-full ${
                step >= stepNumber
                  ? "cursor-pointer bg-[#2e7d32]"
                  : "bg-[#363636]"
              }`}
            />
            {stepNumber < 3 && (
              <div
                className={`h-[2px] w-16 ${
                  step > stepNumber ? "bg-[#2e7d32]" : "bg-[#363636]"
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );

    const renderGuidelines = () => (
      <div className="space-y-6">
        {/* community svg from sidebar */}
        <div className="flex gap-2 items-center">
          <svg
            className="h-5 w-5 text-[#7dd87d]"
            viewBox="0 0 20 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clipPath="url(#clip0_1_486)">
              <path
                d="M4.5 0C5.16304 0 5.79893 0.263392 6.26777 0.732233C6.73661 1.20107 7 1.83696 7 2.5C7 3.16304 6.73661 3.79893 6.26777 4.26777C5.79893 4.73661 5.16304 5 4.5 5C3.83696 5 3.20107 4.73661 2.73223 4.26777C2.26339 3.79893 2 3.16304 2 2.5C2 1.83696 2.26339 1.20107 2.73223 0.732233C3.20107 0.263392 3.83696 0 4.5 0ZM16 0C16.663 0 17.2989 0.263392 17.7678 0.732233C18.2366 1.20107 18.5 1.83696 18.5 2.5C18.5 3.16304 18.2366 3.79893 17.7678 4.26777C17.2989 4.73661 16.663 5 16 5C15.337 5 14.7011 4.73661 14.2322 4.26777C13.7634 3.79893 13.5 3.16304 13.5 2.5C13.5 1.83696 13.7634 1.20107 14.2322 0.732233C14.7011 0.263392 15.337 0 16 0ZM0 9.33438C0 7.49375 1.49375 6 3.33437 6H4.66875C5.16562 6 5.6375 6.10938 6.0625 6.30312C6.02187 6.52812 6.00313 6.7625 6.00313 7C6.00313 8.19375 6.52812 9.26562 7.35625 10C7.35 10 7.34375 10 7.33437 10H0.665625C0.3 10 0 9.7 0 9.33438ZM12.6656 10C12.6594 10 12.6531 10 12.6438 10C13.475 9.26562 13.9969 8.19375 13.9969 7C13.9969 6.7625 13.975 6.53125 13.9375 6.30312C14.3625 6.10625 14.8344 6 15.3313 6H16.6656C18.5063 6 20 7.49375 20 9.33438C20 9.70312 19.7 10 19.3344 10H12.6656ZM7 7C7 6.20435 7.31607 5.44129 7.87868 4.87868C8.44129 4.31607 9.20435 4 10 4C10.7956 4 11.5587 4.31607 12.1213 4.87868C12.6839 5.44129 13 6.20435 13 7C13 7.79565 12.6839 8.55871 12.1213 9.12132C11.5587 9.68393 10.7956 10 10 10C9.20435 10 8.44129 9.68393 7.87868 9.12132C7.31607 8.55871 7 7.79565 7 7ZM4 15.1656C4 12.8656 5.86562 11 8.16562 11H11.8344C14.1344 11 16 12.8656 16 15.1656C16 15.625 15.6281 16 15.1656 16H4.83437C4.375 16 4 15.6281 4 15.1656Z"
                fill="#7dd87d"
              />
            </g>
          </svg>
          <h2 className="text-xl font-semibold text-[#eaeaea]">
            Community Guidelines
          </h2>
        </div>
        {/* name of community */}
        <p className="ml-4 text-sm text-[#b5b5b5]">{community.title.value}</p>
        {renderStepIndicator()}

        <div
          style={{
            maxHeight: "400px",
            scrollbarWidth: "thin",
            scrollbarColor: "#7dd87d #242424",
            overflowY: "auto",
          }}
          className="rounded-lg bg-[#242424] p-4"
        >
          <div
            className="text-[#b5b5b5]"
            dangerouslySetInnerHTML={{
              __html:
                community.requirements?.value ||
                "No specific guidelines provided.",
            }}
          />
        </div>

        <div className="mt-4 space-y-4">
          <label className="flex items-start">
            <input
              type="checkbox"
              checked={joinFormData.agreeToGuidelines}
              onChange={(e) =>
                setJoinFormData((prev) => ({
                  ...prev,
                  agreeToGuidelines: e.target.checked,
                }))
              }
              className="mt-1 mr-2"
            />
            <span className="text-sm text-[#eaeaea]">
              I agree to follow the community guidelines
            </span>
          </label>

          <label className="flex items-start">
            <input
              type="checkbox"
              checked={joinFormData.agreeToTerms}
              onChange={(e) =>
                setJoinFormData((prev) => ({
                  ...prev,
                  agreeToTerms: e.target.checked,
                }))
              }
              className="mt-1 mr-2"
            />
            <span className="text-sm text-[#eaeaea]">
              I agree to the{" "}
              <a href="#" className="text-[#7dd87d]">
                Terms & Conditions
              </a>
            </span>
          </label>
        </div>

        <div className="flex gap-4 justify-end">
          <button
            onClick={() => setStep(1)}
            className="rounded-lg border border-[#363636] px-6 py-2 text-sm text-[#b5b5b5]"
          >
            Back
          </button>
          <button
            onClick={() => setStep(3)}
            disabled={
              !joinFormData.agreeToGuidelines || !joinFormData.agreeToTerms
            }
            className="rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    );

    const renderPayment = () => (
      <div className="space-y-6">
        <div className="flex flex-col justify-center items-center">
          <CrownIcon />
          <h2 className="mt-4 text-center text-xl font-semibold text-[#eaeaea]">
            To Join a Community, Please Pay Your Community Fee
          </h2>
        </div>
        {renderStepIndicator()}

        <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-6">
          <div className="mb-6">
            <h3 className="text-sm font-medium text-[#7dd87d]">Premium Plan</h3>
            <div className="mt-4 text-xl font-bold text-[#a3eca3]">
              $
              {Number(
                community.subscription_fee?.value ||
                  community.privacy_settings?.value?.subscription_fee ||
                  "0"
              ).toFixed(2)}
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex gap-2 items-center">
              <svg
                className="h-5 w-5 text-[#7dd87d]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span className="text-[#eaeaea]">Post Opportunities</span>
            </div>
            <div className="flex gap-2 items-center">
              <svg
                className="h-5 w-5 text-[#7dd87d]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span className="text-[#eaeaea]">Get updated Opportunities</span>
            </div>
            <div className="flex gap-2 items-center">
              <svg
                className="h-5 w-5 text-[#7dd87d]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span className="text-[#eaeaea]">Priority customer support</span>
            </div>
            {community.privacy_settings?.value?.enable_affiliate && (
              <div className="flex gap-2 items-center">
                <svg
                  className="h-5 w-5 text-[#7dd87d]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-[#eaeaea]">
                  Get enrolled into Affiliate Program
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-col gap-3 mt-6">
            <button
              onClick={() => handleSubmit(false)}
              disabled={loading}
              className="w-full rounded-lg bg-[#2e7d32] px-6 py-3 text-center text-sm font-medium text-white hover:bg-[#1b5e20] disabled:opacity-50"
            >
              {loading ? "Processing..." : "Upgrade Now"}
            </button>
            <button
              onClick={() => handleSubmit(true)}
              disabled={loading}
              className="w-full rounded-lg border border-[#363636] px-6 py-3 text-center text-sm text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50"
            >
              Maybe Later
            </button>
            <a
              href="#"
              className="mt-2 text-center text-sm text-[#7dd87d] hover:underline"
            >
              View all plan features
            </a>
          </div>
        </div>
      </div>
    );

    return (
      <div className="flex fixed inset-0 z-50 justify-center items-center">
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={() => !loading && onClose()}
        />

        <div
          style={{
            scrollbarWidth: "thin",
            scrollbarColor: "#7dd87d #242424",
          }}
          className="relative z-50 max-h-[90vh]  w-full max-w-2xl overflow-y-auto rounded-lg bg-[#252525] p-6 shadow-xl"
        >
          {step === 1 && renderForm()}
          {step === 2 && renderGuidelines()}
          {step === 3 && renderPayment()}
          {showPaymentModal && (
            <PaymentMethodModal
              onClose={() => setShowPaymentModal(false)}
              joinFormData={joinFormData}
              community={community}
            />
          )}
        </div>
      </div>
    );
  };

  const validateForm = () => {
    const errors = {};

    if (paymentType === "card") {
      if (!formData.card_number.trim()) {
        errors.card_number = "Card number is required";
      } else if (!/^\d{16}$/.test(formData.card_number.replace(/\s/g, ""))) {
        errors.card_number = "Invalid card number";
      }

      if (!formData.exp_month.trim()) {
        errors.exp_month = "Expiration month is required";
      } else if (!/^(0[1-9]|1[0-2])$/.test(formData.exp_month)) {
        errors.exp_month = "Invalid month (01-12)";
      }

      if (!formData.exp_year.trim()) {
        errors.exp_year = "Expiration year is required";
      } else if (!/^\d{4}$/.test(formData.exp_year)) {
        errors.exp_year = "Invalid year format (YYYY)";
      }

      if (!formData.cvc.trim()) {
        errors.cvc = "CVC is required";
      } else if (!/^\d{3,4}$/.test(formData.cvc)) {
        errors.cvc = "Invalid CVC";
      }

      if (!formData.name.trim()) {
        errors.name = "Cardholder name is required";
      }
    } else {
      // For PayPal, no additional validation needed as it will redirect
    }

    return errors;
  };
  const handleAddPaymentMethod = async (e) => {
    e.preventDefault();

    const errors = validateForm();
    setFormErrors(errors);

    if (Object.keys(errors).length === 0) {
      setIsSubmitting(true);

      try {
        const sdk = new MkdSDK();
        const payload = {
          type: { value: paymentType },
          is_default: { value: formData.is_default },
        };

        if (paymentType === "card") {
          payload.card_number = {
            value: formData.card_number.replace(/\s/g, ""),
          };
          payload.exp_month = { value: formData.exp_month };
          payload.exp_year = { value: formData.exp_year };
          payload.cvc = { value: formData.cvc };
          payload.name = { value: formData.name };
        }

        const response = await sdk.AddPaymentMethod(payload);
        showToast(
          globalDispatch,
          "Payment method added successfully",
          5000,
          "success"
        );

        // Refresh payment methods
        const methodsData = await sdk.GetPaymentMethods();
        setPaymentMethods(methodsData.list);

        // Close modal and reset form
        setShowPaymentMethodModal(false);
        setFormData({
          card_number: "",
          exp_month: "",
          exp_year: "",
          cvc: "",
          name: "",
          is_default: false,
        });
      } catch (error) {
        showToast(globalDispatch, error.message, 5000, "error");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const PaymentMethodModal = ({ onClose, joinFormData, community }) => {
    const [localFormData, setLocalFormData] = useState({
      card_number: "",
      exp_month: "",
      exp_year: "",
      cvc: "",
      name: "",
      is_default: false,
    });
    const [localPaymentType, setLocalPaymentType] = useState("card");
    const [localFormErrors, setLocalFormErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Format card number in groups of 4
    const formatCardNumber = (value) => {
      const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
      const matches = v.match(/\d{4,16}/g);
      const match = (matches && matches[0]) || "";
      const parts = [];

      for (let i = 0, len = match.length; i < len; i += 4) {
        parts.push(match.substring(i, i + 4));
      }

      if (parts.length) {
        return parts.join(" ");
      } else {
        return value;
      }
    };

    const handleLocalInputChange = (e) => {
      const { name, value, type, checked } = e.target;

      if (type === "checkbox") {
        setLocalFormData((prev) => ({
          ...prev,
          [name]: checked,
        }));
        return;
      }

      let formattedValue = value;

      switch (name) {
        case "card_number":
          formattedValue = formatCardNumber(value);
          if (formattedValue.replace(/\s/g, "").length > 16) return;
          break;
        case "exp_month":
          // Remove any non-digit characters
          const digitsOnly = value.replace(/\D/g, '');

          // Empty input case
          if (digitsOnly === "") {
            formattedValue = "";
            break;
          }

          // Don't allow more than 2 digits
          if (digitsOnly.length > 2) return;

          // Parse the numeric value (for validation)
          const monthNum = parseInt(digitsOnly);

          // Handle single digit input (0-9)
          if (digitsOnly.length === 1) {
            // Allow single digit as-is to make editing easier
            formattedValue = digitsOnly;
            break;
          }

          // Handle two-digit input
          if (digitsOnly.length === 2) {
            // Only allow valid months (01-12)
            if (monthNum < 1 || monthNum > 12) return;
            formattedValue = digitsOnly;
            break;
          }

          // Default case (shouldn't reach here)
          formattedValue = digitsOnly;
          break;
        case "exp_year":
          // Only allow 4 digits for year
          if (!/^\d{0,4}$/.test(value)) return;
          break;
        case "cvc":
          // Only allow 3 digits for CVC
          if (!/^\d{0,3}$/.test(value)) return;
          break;
        default:
          break;
      }

      setLocalFormData((prev) => ({
        ...prev,
        [name]: formattedValue,
      }));
    };

    const handleLocalPaymentTypeChange = (type) => {
      setLocalPaymentType(type);
      setLocalFormErrors({});
    };

    const handleModalClose = (e) => {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }
      onClose();
    };

    const validateLocalForm = () => {
      const errors = {};

      if (localPaymentType === "card") {
        if (!localFormData.card_number.trim()) {
          errors.card_number = "Card number is required";
        } else if (
          !/^\d{16}$/.test(localFormData.card_number.replace(/\s/g, ""))
        ) {
          errors.card_number = "Invalid card number";
        }

        if (!localFormData.exp_month.trim()) {
          errors.exp_month = "Expiration month is required";
        } else if (!/^(0[1-9]|1[0-2])$/.test(localFormData.exp_month)) {
          errors.exp_month = "Invalid month (01-12)";
        }

        if (!localFormData.exp_year.trim()) {
          errors.exp_year = "Expiration year is required";
        } else if (!/^\d{4}$/.test(localFormData.exp_year)) {
          errors.exp_year = "Invalid year format (YYYY)";
        }

        if (!localFormData.cvc.trim()) {
          errors.cvc = "CVC is required";
        } else if (!/^\d{3,4}$/.test(localFormData.cvc)) {
          errors.cvc = "Invalid CVC";
        }

        if (!localFormData.name.trim()) {
          errors.name = "Cardholder name is required";
        }
      }

      return errors;
    };

    const handleSubmitPayment = async (e) => {
      e.preventDefault();
      e.stopPropagation();

      const errors = validateLocalForm();
      setLocalFormErrors(errors);

      if (Object.keys(errors).length === 0) {
        setIsSubmitting(true);

        try {
          const sdk = new MkdSDK();
          // First add the payment method
          const paymentPayload = {
            type: { value: localPaymentType },
            is_default: { value: localFormData.is_default },
          };

          if (localPaymentType === "card") {
            paymentPayload.card_number = {
              value: localFormData.card_number.replace(/\s/g, ""),
            };
            paymentPayload.exp_month = { value: localFormData.exp_month };
            paymentPayload.exp_year = { value: localFormData.exp_year };
            paymentPayload.cvc = { value: localFormData.cvc };
            paymentPayload.name = { value: localFormData.name };
          }

          const paymentResponse = await sdk.AddPaymentMethod(paymentPayload);

          if (!paymentResponse.error) {
            showToast(
              globalDispatch,
              "Payment method added successfully",
              5000,
              "success"
            );

            // Now proceed with joining community with all form data
            const joinCommunityPayload = {
              community_id: { value: community.id.value },
              first_name: { value: joinFormData.firstName },
              last_name: { value: joinFormData.lastName },
              industry: { value: joinFormData.industry },
              company_size: { value: joinFormData.employeeCount },
              meeting_commitment: { value: joinFormData.meetingCommitment },
              additional_notes: { value: joinFormData.additionalNotes },
              goals: {
                value: {
                  networking: joinFormData.goals.buildPartnerships,
                  businessDevelopment: joinFormData.goals.gainAccess,
                  learningAndDevelopment: joinFormData.goals.getResources,
                },
              },
              skip_payment: false,
            };

            const joinResponse = await sdk.JoinCommunity(joinCommunityPayload);

            if (!joinResponse.error) {
              showToast(
                globalDispatch,
                "Successfully joined community!",
                5000,
                "success"
              );
              onClose();
              navigate("/member/communities");
              // window.location.reload(); // Add page refresh

              setShowJoinModal(false);
            } else {
              showToast(
                globalDispatch,
                joinResponse.message || "Failed to join community",
                5000,
                "error"
              );
              onClose();
              navigate("/member/communities");
              // window.location.reload(); // Add page refresh

              setShowJoinModal(false);
            }
          } else {
            showToast(
              globalDispatch,
              paymentResponse.message || "Failed to add payment method",
              5000,
              "error"
            );
          }
        } catch (error) {
          showToast(
            globalDispatch,
            error.message || "Failed to process request",
            5000,
            "error"
          );
        } finally {
          setIsSubmitting(false);
        }
      }
    };

    return (
      <div
        className="flex fixed inset-0 z-50 justify-center items-center bg-black/50"
        onClick={handleModalClose}
      >
        <div
          className="w-full max-w-md rounded-lg bg-[#161616] p-6"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-[#eaeaea]">
              Add Payment Method
            </h2>
            <button
              onClick={handleModalClose}
              className="text-[#b5b5b5] hover:text-[#eaeaea]"
            >
              ×
            </button>
          </div>

          <div className="mt-4">
            <div className="mb-4 flex rounded bg-[#242424]">
              <button
                type="button"
                className={`flex-1 rounded-l py-2 text-sm ${
                  localPaymentType === "card"
                    ? "bg-[#2e7d32] text-[#eaeaea]"
                    : "text-[#b5b5b5]"
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleLocalPaymentTypeChange("card");
                }}
              >
                Credit Card
              </button>
              <button
                type="button"
                className={`flex-1 rounded-r py-2 text-sm ${
                  localPaymentType === "paypal"
                    ? "bg-[#2e7d32] text-[#eaeaea]"
                    : "text-[#b5b5b5]"
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleLocalPaymentTypeChange("paypal");
                }}
              >
                PayPal
              </button>
            </div>

            <form onSubmit={handleSubmitPayment}>
              {localPaymentType === "card" ? (
                <>
                  <div className="mb-3">
                    <label className="mb-1 block text-sm text-[#b5b5b5]">
                      Card Number
                    </label>
                    <input
                      type="text"
                      name="card_number"
                      value={localFormData.card_number}
                      onChange={handleLocalInputChange}
                      placeholder="1234 5678 9012 3456"
                      maxLength={19} // 16 digits + 3 spaces
                      className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                    />
                    {localFormErrors.card_number && (
                      <p className="mt-1 text-xs text-red-500">
                        {localFormErrors.card_number}
                      </p>
                    )}
                  </div>

                  <div className="flex gap-3 mb-3">
                    <div className="flex-1">
                      <label className="mb-1 block text-sm text-[#b5b5b5]">
                        Exp. Month
                      </label>
                      <input
                        type="text"
                        name="exp_month"
                        value={localFormData.exp_month}
                        onChange={handleLocalInputChange}
                        onBlur={(e) => {
                          // Add leading zero when field loses focus if it's a single digit
                          const value = e.target.value;
                          if (value.length === 1 && /^[1-9]$/.test(value)) {
                            setLocalFormData(prev => ({
                              ...prev,
                              exp_month: `0${value}`
                            }));
                          }
                        }}
                        placeholder="MM"
                        maxLength={2}
                        className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                      />
                      {localFormErrors.exp_month && (
                        <p className="mt-1 text-xs text-red-500">
                          {localFormErrors.exp_month}
                        </p>
                      )}
                    </div>

                    <div className="flex-1">
                      <label className="mb-1 block text-sm text-[#b5b5b5]">
                        Exp. Year
                      </label>
                      <input
                        type="text"
                        name="exp_year"
                        value={localFormData.exp_year}
                        onChange={handleLocalInputChange}
                        placeholder="YYYY"
                        maxLength={4}
                        className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                      />
                      {localFormErrors.exp_year && (
                        <p className="mt-1 text-xs text-red-500">
                          {localFormErrors.exp_year}
                        </p>
                      )}
                    </div>

                    <div className="w-20">
                      <label className="mb-1 block text-sm text-[#b5b5b5]">
                        CVC
                      </label>
                      <input
                        type="text"
                        name="cvc"
                        value={localFormData.cvc}
                        onChange={handleLocalInputChange}
                        placeholder="123"
                        maxLength={3}
                        className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                      />
                      {localFormErrors.cvc && (
                        <p className="mt-1 text-xs text-red-500">
                          {localFormErrors.cvc}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className="mb-1 block text-sm text-[#b5b5b5]">
                      Cardholder Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={localFormData.name}
                      onChange={handleLocalInputChange}
                      placeholder="John Doe"
                      className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                    />
                    {localFormErrors.name && (
                      <p className="mt-1 text-xs text-red-500">
                        {localFormErrors.name}
                      </p>
                    )}
                  </div>
                </>
              ) : (
                <div className="rounded bg-[#242424] p-4 text-center text-sm text-[#b5b5b5]">
                  You will be redirected to PayPal to complete setup
                </div>
              )}

              <div className="flex items-center mt-4">
                <input
                  type="checkbox"
                  id="is_default"
                  name="is_default"
                  checked={localFormData.is_default}
                  onChange={handleLocalInputChange}
                  className="h-4 w-4 rounded bg-[#242424]"
                />
                <label
                  htmlFor="is_default"
                  className="ml-2 text-sm text-[#b5b5b5]"
                >
                  Set as default payment method
                </label>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="mt-6 w-full rounded bg-[#2e7d32] py-2 text-sm text-[#eaeaea] disabled:opacity-50"
              >
                {isSubmitting ? "Processing..." : "Add Payment Method"}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  };

  const handleManageCommunity = (communityId) => {
    navigate(`/member/communities/edit/${communityId}`);
  };

  const loadCommunityMembers = async (communityId) => {
    try {
      setLoadingMembers(true);
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/community/${communityId}/users`,
        {},
        "GET"
      );
      if (!response.error) {
        setCommunityMembers(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load members:", err);
      setLoadingMembers(false);
      showToast(globalDispatch, err.message, 5000, "error");
    } finally {
      setLoadingMembers(false);
    }
  };

  const handleRemoveMember = async (memberId) => {
    try {
      const sdk = new MkdSDK();
      const requestBody = {
        user_id: { value: memberId },
        remove: { value: true },
        role: { value: "member" }
      };

      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/community/${selectedCommunity.id.value}/update`,
        requestBody,
        "POST"
      );

      if (response.error) {
        showToast(
          globalDispatch,
          response.message || "Failed to remove member",
          5000,
          "error"
        );
        return;
      }

      loadCommunityMembers(selectedCommunity.id.value);
    } catch (err) {
      console.error("Failed to remove member:", err);
      showToast(
        globalDispatch,
        err.message || "Failed to remove member",
        5000,
        "error"
      );
    }
  };

  const handleRoleChange = async (memberId, newRole) => {
    try {
      const sdk = new MkdSDK();
      const requestBody = {
        user_id: { value: memberId },
        remove: { value: false },
        role: { value: newRole }
      };

      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/community/${selectedCommunity.id.value}/update`,
        requestBody,
        "POST"
      );

      if (response.error) {
        showToast(
          globalDispatch,
          response.message || "Failed to update role",
          5000,
          "error"
        );
        return;
      }

      loadCommunityMembers(selectedCommunity.id.value);
    } catch (err) {
      console.error("Failed to update role:", err);
      showToast(
        globalDispatch,
        err.message || "Failed to update role",
        5000,
        "error"
      );
    }
  };

  // Member management functions moved to AddMembersForm component

  const MembersModal = ({ isOpen, onClose }) => {
    const [showAddMembersForm, setShowAddMembersForm] = useState(false);

    if (!isOpen) return null;

    return (
      <div className="flex fixed inset-0 z-50 justify-center items-center">
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        <div className="relative z-50 w-full max-w-4xl rounded-lg bg-[#161616] p-6 shadow-xl">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold text-[#eaeaea] mr-4">
                Community Members
              </h2>
              <button
                onClick={() => setShowAddMembersForm(!showAddMembersForm)}
                className="rounded-lg bg-[#7dd87d] px-4 py-1.5 text-sm text-[#1e1e1e] hover:bg-[#7dd87d]/90"
              >
                Add Members
              </button>
            </div>
            <button
              onClick={onClose}
              className="text-[#b5b5b5] hover:text-[#eaeaea]"
            >
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          {showAddMembersForm && (
            <AddMembersForm
              communityId={selectedCommunity.id.value}
              onClose={() => setShowAddMembersForm(false)}
              onSuccess={() => loadCommunityMembers(selectedCommunity.id.value)}
            />
          )}

          {loadingMembers ? (
            <div className="flex justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-[#7dd87d] border-t-transparent"></div>
            </div>
          ) : (
            <div className="max-h-[60vh] overflow-y-auto">
              <table className="w-full">
                <thead className="border-b border-[#363636] text-left">
                  <tr>
                    <th className="pb-3 text-sm font-medium text-[#b5b5b5]">
                      Member
                    </th>
                    <th className="pb-3 text-sm font-medium text-[#b5b5b5]">
                      Role
                    </th>
                    <th className="pb-3 text-sm font-medium text-[#b5b5b5]">
                      Industry
                    </th>
                    <th className="pb-3 text-sm font-medium text-[#b5b5b5]">
                      Joined
                    </th>
                    <th className="pb-3 text-sm font-medium text-[#b5b5b5]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {communityMembers.map((member) => (
                    <tr
                      key={`${member.id.value}-${member.joined_at.value}`}
                      className="border-b border-[#363636]"
                    >
                      <td className="py-4">
                        <div className="flex gap-3 items-center">
                          <UserAvatar user={member} />
                          <span className="text-[#eaeaea]">
                            {member.name.value}
                          </span>
                        </div>
                      </td>
                      <td className="py-4">
                        <select
                          value={member.role.value}
                          onChange={(e) =>
                            handleRoleChange(member.id.value, e.target.value)
                          }
                          className="rounded bg-[#242424] px-2 py-1 text-sm text-[#eaeaea]"
                        >
                          <option value="member">Member</option>
                          <option value="admin">Admin</option>
                        </select>
                      </td>
                      <td className="py-4 text-[#eaeaea]">
                        {member.industry?.value || "N/A"}
                      </td>
                      <td className="py-4 text-[#eaeaea]">
                        {formatDate(member.joined_at.value)}
                      </td>
                      <td className="py-4">
                        <button
                          onClick={() => handleRemoveMember(member.id.value)}
                          className="text-red-500 hover:text-red-400"
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    );
  };

  const handleNewPost = () => {
    const communityId = selectedCommunity?.id?.value;
    navigate("/member/referrals/add", {
      state: {
        referralType: "community referral",
        communityId: communityId
      }
    });
  };

  const handleViewDetails = (post) => {
    setSelectedPost(post);
    setShowDetailsModal(true);
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4">
      {error && <Toast message={error} type="error" />}

      {/* Render the recommendation form as a modal */}
      {showRecommendForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={() => {
                setShowRecommendForm(false);
                setSelectedReferralId(null);
              }}
              aria-hidden="true"
            />
            <div className="relative z-50 w-full max-w-3xl transform overflow-hidden">
              <div className="relative bg-[#1e1e1e] rounded-lg shadow-xl">
                <ReferralRecommendForm
                  referralId={selectedReferralId}
                  onClose={() => {
                    setShowRecommendForm(false);
                    setSelectedReferralId(null);
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {renderHeader()}
      {renderTabs()}
      {showCommunityView ? (
        renderCommunityView()
      ) : activeTab === "join" ? (
        renderGridView()
      ) : (
        renderCommunityList()
      )}

      {/* Other modals */}
      <JoinCommunityModal
        isOpen={showJoinModal}
        onClose={() => setShowJoinModal(false)}
        community={selectedCommunity}
      />
      <MembersModal
        isOpen={showMembersModal}
        onClose={() => setShowMembersModal(false)}
      />
      <RepostModal
        isOpen={showRepostModal}
        onClose={(refresh) => {
          setShowRepostModal(false);
          setReferralToRepost(null);
          if (refresh) loadCommunities();
        }}
        referralId={referralToRepost}
        communities={joinedCommunities}
      />
      <PostDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        post={selectedPost}
      />
    </div>
  );
};

export default CommunitiesPage;
