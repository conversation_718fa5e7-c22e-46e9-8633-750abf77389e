import React, { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";

const EmojiIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM7 6C7 6.82843 6.32843 7.5 5.5 7.5C4.67157 7.5 4 6.82843 4 6C4 5.17157 4.67157 4.5 5.5 4.5C6.32843 4.5 7 5.17157 7 6ZM16 6C16 6.82843 15.3284 7.5 14.5 7.5C13.6716 7.5 13 6.82843 13 6C13 5.17157 13.6716 4.5 14.5 4.5C15.3284 4.5 16 5.17157 16 6ZM10 15.5C12.33 15.5 14.32 14.05 15.12 12H4.88C5.68 14.05 7.67 15.5 10 15.5Z" fill="#B5B5B5"/>
  </svg>
);

const AttachmentIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625" stroke="#B5B5B5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
);

const ReferralFormModal = ({ isOpen, onClose, referral = null }) => {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [industries, setIndustries] = useState([]);
  const [formData, setFormData] = useState({
    title: referral?.title?.value || "",
    type: referral?.type?.value || "",
    industry: referral?.industry?.value || "",
    description: referral?.description?.value || "",
    know_client: referral?.know_client?.value || "",
    deal_size: referral?.deal_size?.value || "",
    referral_fee: referral?.referral_fee?.value || "",
    payment_method: referral?.payment_method?.value || "",
    referral_type: referral?.referral_type?.value || "",
    community: referral?.community?.value || "",
    direct_person: referral?.direct_person?.value || "",
    additional_notes: referral?.additional_notes?.value || ""
  });

  useEffect(() => {
    const loadIndustries = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

        if (!response.error && response.data) {
          setIndustries(response.data);
        }
      } catch (err) {
        console.error("Failed to load industries:", err);
      }
    };

    if (isOpen) {
      loadIndustries();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const sdk = new MkdSDK();

      // Prepare the data for the API - convert industry to industry_id as integer
      const apiData = {
        ...formData,
        industry_id: formData.industry ? parseInt(formData.industry, 10) : null,
        // Remove the string industry field since we're sending industry_id
        industry: undefined
      };

      const response = referral
        ? await sdk.EditReferral({ ...apiData, id: referral.id.value })
        : await sdk.CreateReferral(apiData);

      if (!response.error) {
        onClose(true); // Refresh parent list
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError(err.message || "Failed to save referral");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-2xl rounded-lg bg-[#1e1e1e] p-6">
        <h2 className="mb-6 text-xl font-bold text-[#eaeaea]">
          {referral ? "Edit Opportunity" : "Add Opportunity"}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm text-[#b5b5b5]">Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter referral title"
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            />
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Type of Opportunity</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            >
              <option value="">Select type</option>
              <option value="looking_for_service">Looking for Service</option>
              <option value="looking_for_product">Looking for Product</option>
              <option value="looking_for_buyer">Looking for Buyer</option>
              <option value="looking_for_investor">Looking for Investor</option>
              <option value="looking_for_partner">Looking for Partner</option>
            </select>
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Industry</label>
            <select
              value={formData.industry}
              onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            >
              <option value="">Select industry</option>
              {industries.map((industry) => (
                <option key={industry.id} value={industry.id}>
                  {industry.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Description</label>
            <div className="relative">
              <div className="absolute left-4 top-4 flex items-center gap-2">
                <button
                  type="button"
                  className="text-[#b5b5b5] hover:text-[#eaeaea]"
                  onClick={() => {/* Emoji functionality will be added later */}}
                >
                  <EmojiIcon />
                </button>
                <button
                  type="button"
                  className="text-[#b5b5b5] hover:text-[#eaeaea]"
                  onClick={() => {/* Attachment functionality will be added later */}}
                >
                  <AttachmentIcon />
                </button>
              </div>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Write your text here..."
                rows={4}
                className="mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 pl-24 text-[#eaeaea]"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* <div>
              <label className="block text-sm text-[#b5b5b5]">Know Client Personally?</label>
              <select
                value={formData.know_client}
                onChange={(e) => setFormData(prev => ({ ...prev, know_client: e.target.value }))}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
            </div> */}
            <div>
              <label className="block text-sm text-[#b5b5b5]">Estimated Deal Size</label>
              <input
                type="text"
                value={formData.deal_size}
                onChange={(e) => setFormData(prev => ({ ...prev, deal_size: e.target.value }))}
                placeholder="e.g. $500"
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-[#b5b5b5]">Referral Fee (%)</label>
              <input
                type="text"
                value={formData.referral_fee}
                onChange={(e) => setFormData(prev => ({ ...prev, referral_fee: e.target.value }))}
                placeholder="Enter percentage"
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              />
            </div>
            <div>
              <label className="block text-sm text-[#b5b5b5]">Payment Method</label>
              <select
                value={formData.payment_method}
                onChange={(e) => setFormData(prev => ({ ...prev, payment_method: e.target.value }))}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select method</option>
                <option value="bank">Bank Transfer</option>
                <option value="bank">Bank card</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Referral Type</label>
            <select
              value={formData.referral_type}
              onChange={(e) => setFormData(prev => ({ ...prev, referral_type: e.target.value }))}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            >
              <option value="">Select type</option>
              <option value="open">Open Referral</option>
              <option value="direct">Direct Referral</option>
            </select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-[#b5b5b5]">Select Community</label>
              <select
                value={formData.community}
                onChange={(e) => setFormData(prev => ({ ...prev, community: e.target.value }))}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select community</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-[#b5b5b5]">Select Direct Person</label>
              <select
                value={formData.direct_person}
                onChange={(e) => setFormData(prev => ({ ...prev, direct_person: e.target.value }))}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select person</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Additional Notes</label>
            <textarea
              value={formData.additional_notes}
              onChange={(e) => setFormData(prev => ({ ...prev, additional_notes: e.target.value }))}
              placeholder="Write any additional notes here..."
              rows={4}
              className="mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"
            />
          </div>

          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={onClose}
              className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
            >
              {loading ? "Saving..." : "Submit Referral"}
            </button>
          </div>
        </form>

        {error && <Toast message={error} />}
      </div>
    </div>
  );
};

export default ReferralFormModal;